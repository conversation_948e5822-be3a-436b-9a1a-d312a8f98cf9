# Subscription Plan Features API Guide

## Overview
This API allows you to retrieve features for specific subscription plans by plan name.

## API Endpoint

### Get Features by Plan Name
```
GET /api/subscriptions/features/by-plan/{planName}
```

**Parameters:**
- `planName` (string): The name of the subscription plan (case-insensitive)
  - Valid values: `Starter`, `Business`, `Enterprise`

## Plan Features Mapping

### Starter Plan Features ($100/month, ₹8,500/month)
- Work Breakdown Structure (WBS)
- ODC (Other Direct Cost) Table
- Job Start Form
- Input Register
- Email Notifications
- Monthly Progress Review
- Manpower Planning
- User Experience (Basic UI)
- Reporting (Basic Export PDF)
- SLA Support & Updates (Email Only)

### Business Plan Features ($400/month, ₹34,000/month)
- Users Included (Up to 20)
- Work Breakdown Structure (WBS)
- ODC (Other Direct Cost) Table
- Job Start Form
- Estimated Expenses Table
- Input Register
- Email Notifications
- Check & Review Logs
- Monthly Progress Review
- Quarterly Progress Review
- Manpower Planning
- User Experience (Enhanced UX)
- Reporting (Basic Export PDF)
- SLA Support & Updates (Email Only)

### Enterprise Plan Features (Custom pricing)
- Users Included (Unlimited)
- Work Breakdown Structure (WBS)
- WBS version 2.0
- Gantt/Timeline View
- ODC (Other Direct Cost) Table
- Job Start Form
- Estimated Expenses Table
- Input Register
- Email Notifications
- Check & Review Logs
- Change Control Register
- Monthly Progress Review
- Quarterly Progress Review
- Weekly/Daily Progress Review
- Milestone Tracking
- Budget vs Actual Analysis
- Manpower Planning
- API Integration
- User Experience (Tailored UI/UX)
- Reporting (Extended Reporting)
- SLA Support & Updates

## Example API Calls

### 1. Get Starter Plan Features
```bash
curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Starter"
```

**Response:**
```json
[
  {
    "id": 2,
    "name": "Work Breakdown Structure (WBS)",
    "description": null,
    "priceUSD": null,
    "priceINR": null
  },
  {
    "id": 5,
    "name": "ODC (Other Direct Cost) Table",
    "description": null,
    "priceUSD": null,
    "priceINR": null
  },
  {
    "id": 6,
    "name": "Job Start Form",
    "description": null,
    "priceUSD": null,
    "priceINR": null
  }
  // ... more features
]
```

### 2. Get Business Plan Features
```bash
curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Business"
```

### 3. Get Enterprise Plan Features
```bash
curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Enterprise"
```

## Error Responses

### Plan Not Found (404)
```json
{
  "message": "Subscription plan 'InvalidPlan' not found."
}
```

### Server Error (500)
```json
{
  "message": "An error occurred while retrieving plan features"
}
```

## Testing Steps

1. **Start the API server:**
   ```bash
   cd backend/src/NJSAPI
   dotnet run
   ```

2. **Test the endpoint:**
   ```bash
   # Test Starter plan
   curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Starter"
   
   # Test Business plan
   curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Business"
   
   # Test Enterprise plan
   curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/Enterprise"
   
   # Test case-insensitive
   curl -X GET "http://localhost:5245/api/subscriptions/features/by-plan/starter"
   ```

## Database Seeding
The features and plan mappings are automatically seeded when the application starts:
- Features are stored in the `Features` table
- Subscription plans are stored in the `SubscriptionPlans` table
- Feature-to-plan mappings are stored in the `SubscriptionPlanFeatures` table

## Notes
- Plan names are case-insensitive
- The API returns 404 if the plan name is not found
- All endpoints include proper error handling and logging
- The response includes feature ID, name, description, and pricing information
