# Subscription Plan API Testing Guide

## Overview
This document shows how to test the subscription plan APIs that return features based on plan names.

## Available API Endpoints

### 1. Get Features by Plan Name (Simple)
```
GET /api/subscriptions/features/by-plan/{planName}
```

**Example Request:**
```
GET /api/subscriptions/features/by-plan/Starter
```

**Example Response:**
```json
[
  {
    "id": 1,
    "name": "Work Breakdown Structure (WBS)",
    "description": null,
    "priceUSD": null,
    "priceINR": null
  },
  {
    "id": 2,
    "name": "ODC (Other Direct Cost) Table",
    "description": null,
    "priceUSD": null,
    "priceINR": null
  }
]
```

### 2. Get Plan Features Detailed
```
GET /api/subscriptions/plan/{planName}/features-detailed
```

**Example Request:**
```
GET /api/subscriptions/plan/Starter/features-detailed
```

**Example Response:**
```json
{
  "planId": 1,
  "planName": "Starter",
  "description": "Perfect for individuals and small projects",
  "pricing": {
    "monthlyPrice": 100.00,
    "yearlyPrice": 1000.00,
    "monthlyPriceFormatted": "$100.00",
    "yearlyPriceFormatted": "$1000.00"
  },
  "limitations": {
    "maxUsers": 5,
    "maxProjects": 5,
    "maxStorageGB": 10
  },
  "features": [
    {
      "id": 1,
      "name": "Work Breakdown Structure (WBS)",
      "description": null,
      "slug": "work_breakdown_structure_wbs",
      "priceUSD": null,
      "priceINR": null
    }
  ],
  "totalFeatures": 10
}
```

### 3. Get All Plans Payload (Full Format)
```
GET /api/subscriptions/plans-payload
```

**Example Response:**
```json
{
  "plans": [
    {
      "id": "plan_starter_2024",
      "name": "Starter",
      "slug": "starter",
      "description": "Perfect for individuals and small projects",
      "pricing": {
        "monthly": {
          "amount": 10000,
          "currency": "USD",
          "formatted": "$100.00"
        },
        "yearly": {
          "amount": 100000,
          "currency": "USD",
          "formatted": "$1000.00",
          "savings": {
            "amount": 20000,
            "percentage": 17
          }
        }
      },
      "features": [
        {
          "id": "work_breakdown_structure_wbs",
          "name": "Work Breakdown Structure (WBS)"
        }
      ],
      "limitations": {
        "projects": 5,
        "storage_gb": 10,
        "max_users": 5
      }
    }
  ]
}
```

## Starter Plan Features
Based on the seeding data, the Starter plan includes:

1. **Work Breakdown Structure (WBS)** - Core project planning feature
2. **ODC (Other Direct Cost) Table** - Cost tracking
3. **Job Start Form** - Project initiation
4. **Input Register** - Data entry management
5. **Email Notifications** - Basic communication
6. **Monthly Progress Review** - Progress tracking
7. **Manpower Planning** - Resource management
8. **User Experience** - Basic UI
9. **Reporting** - Basic export functionality
10. **SLA Support & Updates** - Email-only support

## Testing Steps

1. **Start the API server:**
   ```bash
   cd backend/src/NJSAPI
   dotnet run
   ```

2. **Test the endpoints using curl or Postman:**
   ```bash
   # Test simple features endpoint
   curl -X GET "http://localhost:5000/api/subscriptions/features/by-plan/Starter"
   
   # Test detailed features endpoint
   curl -X GET "http://localhost:5000/api/subscriptions/plan/Starter/features-detailed"
   
   # Test full payload endpoint
   curl -X GET "http://localhost:5000/api/subscriptions/plans-payload"
   ```

3. **Verify the response contains all expected Starter plan features**

## Database Seeding
The features are automatically seeded when the application starts. The seeding includes:
- All feature definitions in the `Features` table
- Subscription plans (Starter, Business, Enterprise) in the `SubscriptionPlans` table  
- Feature-to-plan mappings in the `SubscriptionPlanFeatures` table

## Notes
- Plan names are case-insensitive
- The API returns 404 if the plan name is not found
- All endpoints include proper error handling and logging
