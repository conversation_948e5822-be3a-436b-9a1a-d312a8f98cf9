# PowerShell script to test the Subscription Plan Features API

$baseUrl = "http://localhost:5245"

Write-Host "Testing Subscription Plan Features API" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Test 1: Get Starter plan features
Write-Host "`n1. Testing: GET /api/subscriptions/features/by-plan/Starter" -ForegroundColor Yellow
try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/Starter" -Method GET
    Write-Host "✅ Success! Found $($response1.Count) features for Starter plan" -ForegroundColor Green
    Write-Host "Starter Plan Features:" -ForegroundColor Cyan
    $response1 | ForEach-Object { Write-Host "   - $($_.name)" -ForegroundColor White }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get Business plan features
Write-Host "`n2. Testing: GET /api/subscriptions/features/by-plan/Business" -ForegroundColor Yellow
try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/Business" -Method GET
    Write-Host "✅ Success! Found $($response2.Count) features for Business plan" -ForegroundColor Green
    Write-Host "Business Plan Features:" -ForegroundColor Cyan
    $response2 | ForEach-Object { Write-Host "   - $($_.name)" -ForegroundColor White }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get Enterprise plan features
Write-Host "`n3. Testing: GET /api/subscriptions/features/by-plan/Enterprise" -ForegroundColor Yellow
try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/Enterprise" -Method GET
    Write-Host "✅ Success! Found $($response3.Count) features for Enterprise plan" -ForegroundColor Green
    Write-Host "Enterprise Plan Features:" -ForegroundColor Cyan
    $response3 | ForEach-Object { Write-Host "   - $($_.name)" -ForegroundColor White }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test case-insensitive plan name
Write-Host "`n4. Testing: Case-insensitive plan name (starter)" -ForegroundColor Yellow
try {
    $response4 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/starter" -Method GET
    Write-Host "✅ Success! Case-insensitive search works - Found $($response4.Count) features" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test non-existent plan (should return 404)
Write-Host "`n5. Testing: Non-existent plan (should return 404)" -ForegroundColor Yellow
try {
    $response5 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/NonExistent" -Method GET
    Write-Host "❌ Unexpected success - should have returned 404" -ForegroundColor Red
}
catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "✅ Success! Correctly returned 404 for non-existent plan" -ForegroundColor Green
    } else {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=======================================" -ForegroundColor Green
Write-Host "API Testing Complete!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Display summary
Write-Host "`nAPI Endpoint Summary:" -ForegroundColor Yellow
Write-Host "URL: GET $baseUrl/api/subscriptions/features/by-plan/{planName}" -ForegroundColor White
Write-Host "Valid Plan Names: Starter, Business, Enterprise (case-insensitive)" -ForegroundColor White
Write-Host "`nTo test manually:" -ForegroundColor Yellow
Write-Host "1. Open Swagger UI: $baseUrl/swagger" -ForegroundColor White
Write-Host "2. Find 'Subscriptions' section" -ForegroundColor White
Write-Host "3. Test the 'GET /api/subscriptions/features/by-plan/{planName}' endpoint" -ForegroundColor White
