# PowerShell script to test the Subscription Plan API endpoints

$baseUrl = "http://localhost:5000"

Write-Host "Testing Subscription Plan API Endpoints" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Test 1: Get features by plan name (simple)
Write-Host "`n1. Testing: GET /api/subscriptions/features/by-plan/Starter" -ForegroundColor Yellow
try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/Starter" -Method GET
    Write-Host "✅ Success! Found $($response1.Count) features for Starter plan" -ForegroundColor Green
    $response1 | ForEach-Object { Write-Host "   - $($_.name)" -ForegroundColor Cyan }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get plan features detailed
Write-Host "`n2. Testing: GET /api/subscriptions/plan/Starter/features-detailed" -ForegroundColor Yellow
try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/plan/Starter/features-detailed" -Method GET
    Write-Host "✅ Success! Plan: $($response2.planName)" -ForegroundColor Green
    Write-Host "   Description: $($response2.description)" -ForegroundColor Cyan
    Write-Host "   Monthly Price: $($response2.pricing.monthlyPriceFormatted)" -ForegroundColor Cyan
    Write-Host "   Total Features: $($response2.totalFeatures)" -ForegroundColor Cyan
    Write-Host "   Max Users: $($response2.limitations.maxUsers)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get all plans payload
Write-Host "`n3. Testing: GET /api/subscriptions/plans-payload" -ForegroundColor Yellow
try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/plans-payload" -Method GET
    Write-Host "✅ Success! Found $($response3.plans.Count) plans" -ForegroundColor Green
    $response3.plans | ForEach-Object { 
        Write-Host "   Plan: $($_.name) - $($_.pricing.monthly.formatted)/month" -ForegroundColor Cyan 
        Write-Host "     Features: $($_.features.Count)" -ForegroundColor Gray
    }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test case-insensitive plan name
Write-Host "`n4. Testing: Case-insensitive plan name (starter)" -ForegroundColor Yellow
try {
    $response4 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/starter" -Method GET
    Write-Host "✅ Success! Case-insensitive search works" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test non-existent plan
Write-Host "`n5. Testing: Non-existent plan (should return 404)" -ForegroundColor Yellow
try {
    $response5 = Invoke-RestMethod -Uri "$baseUrl/api/subscriptions/features/by-plan/NonExistent" -Method GET
    Write-Host "❌ Unexpected success - should have returned 404" -ForegroundColor Red
}
catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "✅ Success! Correctly returned 404 for non-existent plan" -ForegroundColor Green
    } else {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=======================================" -ForegroundColor Green
Write-Host "API Testing Complete!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
