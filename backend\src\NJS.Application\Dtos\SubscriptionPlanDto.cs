using System.ComponentModel.DataAnnotations;

namespace NJS.Application.DTOs
{
    public class SubscriptionPlanDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Slug => Name?.ToLowerInvariant().Replace(" ", "_");
        public decimal MonthlyPrice { get; set; }
        public decimal YearlyPrice { get; set; }
        public int MaxUsers { get; set; }
        public int MaxProjects { get; set; }
        public int MaxStorageGB { get; set; }
        public bool IsActive { get; set; }
        public string StripePriceId { get; set; }
        public List<FeatureDto> Features { get; set; } = new List<FeatureDto>();
    }

    public class SubscriptionPlansResponseDto
    {
        public List<SubscriptionPlanDto> Plans { get; set; } = new List<SubscriptionPlanDto>();
    }
}
